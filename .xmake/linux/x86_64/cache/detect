{
    find_package_linux_x86_64_fetch_package_xmake = {
        ["xmake::ca-certificates_e9ba281ae183402a80dc072d4d571c20_release_20250131_external"] = {
            version = "20250131"
        },
        ["xmake::libffi_46686cf534074f4ebf90e4ab1f9662dd_release_3.4.8_external"] = {
            links = {
                "ffi"
            },
            version = "3.4.8",
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libffi/3.4.8/46686cf534074f4ebf90e4ab1f9662dd/lib"
            },
            static = true,
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libffi/3.4.8/46686cf534074f4ebf90e4ab1f9662dd/include"
            },
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libffi/3.4.8/46686cf534074f4ebf90e4ab1f9662dd/lib/libffi.a"
            },
            license = "MIT"
        },
        ["xmake::glad_9e3539cb78a3425b96246554411a0b79_release_v0.1.36_external"] = {
            linkdirs = {
                "/home/<USER>/.xmake/packages/g/glad/v0.1.36/9e3539cb78a3425b96246554411a0b79/lib"
            },
            version = "v0.1.36",
            syslinks = "dl",
            links = {
                "glad"
            },
            static = true,
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/g/glad/v0.1.36/9e3539cb78a3425b96246554411a0b79/include"
            },
            libfiles = {
                "/home/<USER>/.xmake/packages/g/glad/v0.1.36/9e3539cb78a3425b96246554411a0b79/lib/libglad.a"
            },
            license = "MIT"
        },
        ["xmake::opengl_4e0143c97b65425b855ad5fd03038b6a_release_latest_external"] = false,
        ["xmake::zlib_994fafa590ed48ac9f71516cc846d155_release_v1.3.1_external"] = {
            links = {
                "z"
            },
            version = "v1.3.1",
            linkdirs = {
                "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/lib"
            },
            static = true,
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/include"
            },
            libfiles = {
                "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/lib/libz.a"
            },
            license = "zlib"
        },
        ["xmake::openssl_6c51ab6278e2479b883dffafac69fdaf_release_1.1.1-w_external"] = {
            linkdirs = {
                "/home/<USER>/.xmake/packages/o/openssl/1.1.1-w/6c51ab6278e2479b883dffafac69fdaf/lib"
            },
            version = "1.1.1-w",
            syslinks = {
                "pthread",
                "dl"
            },
            links = {
                "ssl",
                "crypto"
            },
            static = true,
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/o/openssl/1.1.1-w/6c51ab6278e2479b883dffafac69fdaf/include"
            },
            libfiles = {
                "/home/<USER>/.xmake/packages/o/openssl/1.1.1-w/6c51ab6278e2479b883dffafac69fdaf/lib/libssl.a",
                "/home/<USER>/.xmake/packages/o/openssl/1.1.1-w/6c51ab6278e2479b883dffafac69fdaf/lib/libcrypto.a"
            },
            license = "Apache-2.0"
        },
        ["xmake::libpthread-stubs_e6c09af522624eb1ae863fe21c4aa62c_release_0.5_external"] = false,
        ["xmake::util-macros_21ebacb0450b4b16b06b586ecc142d3f_release_1.20.0_external"] = false
    },
    find_program = {
        emerge = false,
        tar = "/nix/store/r8d70l24x06f4fgq246wz2s8zvmj6vxb-gnutar-1.35/bin/tar",
        pacman = false,
        git = "/run/current-system/sw/bin/git",
        nim = false,
        gzip = "/nix/store/31h4wbbp0yvsrciyw46iij6r4c0wc8fp-gzip-1.14/bin/gzip",
        ["pkg-config"] = "/nix/store/rxz7i1sj19nrcmyk4sg49i2h1vfaazby-pkg-config-wrapper-0.29.2/bin/pkg-config",
        ping = "/run/current-system/sw/bin/ping",
        brew = false,
        dpkg = false,
        gcc = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc"
    },
    find_program_fetch_package_system = {
        cmake = "/nix/store/hvn9cprr3p61gjlahk9pq4bmp3zaxj4a-cmake-3.31.7/bin/cmake",
        ["pkg-config"] = "/nix/store/rxz7i1sj19nrcmyk4sg49i2h1vfaazby-pkg-config-wrapper-0.29.2/bin/pkg-config"
    },
    find_programver_fetch_package_system = {
        ["/nix/store/rxz7i1sj19nrcmyk4sg49i2h1vfaazby-pkg-config-wrapper-0.29.2/bin/pkg-config"] = "0.29.2",
        ["/nix/store/hvn9cprr3p61gjlahk9pq4bmp3zaxj4a-cmake-3.31.7/bin/cmake"] = "3.31.7"
    },
    find_package_linux_x86_64_fetch_package_system = {
        ["apt::xutils-dev_21ebacb0450b4b16b06b586ecc142d3f_release_external"] = false,
        opengl_4e0143c97b65425b855ad5fd03038b6a_release_external = {
            linkdirs = {
                "/nix/store/iyy1g70fhkz3hsrckbmbqgxik1j9779c-libglvnd-1.7.0/lib"
            },
            links = {
                "OpenGL"
            },
            shared = true,
            version = "4.5",
            libfiles = {
                "/nix/store/iyy1g70fhkz3hsrckbmbqgxik1j9779c-libglvnd-1.7.0/lib/libOpenGL.so"
            },
            sysincludedirs = {
                "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include"
            }
        }
    },
    ["detect.sdks.find_vcpkgdir"] = false,
    find_program_fetch_package_xmake = {
        ninja = false,
        python3 = false,
        python2 = false,
        python = false,
        cmake = false,
        ["pkg-config"] = false
    }
}