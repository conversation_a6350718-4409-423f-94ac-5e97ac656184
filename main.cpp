// ==== このブロックをコピーして、main.cppの先頭に貼り付けてください ====

#include <iostream>
#include <cstring>
#include <glad/glad.h>
#include <GLFW/glfw3.h>
#include <GL/gl.h>
#include <imgui.h>
#include <imgui_impl_glfw.h>
#include <imgui_impl_opengl3.h>

// エラーコールバック
static void glfw_error_callback(int error, const char* description) {
    fprintf(stderr, "Glfw Error %d: %s\n", error, description);
}

// フレームバッファオブジェクト（FBO）を管理する構造体
struct Framebuffer {
    GLuint ID = 0;
    GLuint TextureID = 0;
    int Width = 0;
    int Height = 0;
};

// FBOを作成するヘルパー関数
bool CreateFramebuffer(Framebuffer& fb, int width, int height) {
    fb.Width = width;
    fb.Height = height;

    glGenFramebuffers(1, &fb.ID);
    glBindFramebuffer(GL_FRAMEBUFFER, fb.ID);

    glGenTextures(1, &fb.TextureID);
    glBindTexture(GL_TEXTURE_2D, fb.TextureID);
    glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, width, height, 0, GL_RGBA, GL_UNSIGNED_BYTE, NULL);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
    glFramebufferTexture2D(GL_FRAMEBUFFER, GL_COLOR_ATTACHMENT0, GL_TEXTURE_2D, fb.TextureID, 0);

    if (glCheckFramebufferStatus(GL_FRAMEBUFFER) != GL_FRAMEBUFFER_COMPLETE) {
        std::cerr << "ERROR::FRAMEBUFFER:: Framebuffer is not complete!" << std::endl;
        glBindFramebuffer(GL_FRAMEBUFFER, 0);
        return false;
    }

    glBindFramebuffer(GL_FRAMEBUFFER, 0);
    return true;
}

// FBOを破棄するヘルパー関数
void DestroyFramebuffer(Framebuffer& fb) {
    glDeleteFramebuffers(1, &fb.ID);
    glDeleteTextures(1, &fb.TextureID);
    fb.ID = 0;
    fb.TextureID = 0;
}


int main() {
    // ------------------------------------------------------------------
    // 1. GLFW, GLAD, ウィンドウの初期化
    // ------------------------------------------------------------------
    glfwSetErrorCallback(glfw_error_callback);
    if (!glfwInit()) return 1;

    const char* glsl_version = "#version 330";
    glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 3);
    glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 3);
    glfwWindowHint(GLFW_OPENGL_PROFILE, GLFW_OPENGL_CORE_PROFILE);

    GLFWwindow* window = glfwCreateWindow(1280, 720, "ImGui in ImGui (VM Example)", NULL, NULL);
    if (window == NULL) return 1;
    glfwMakeContextCurrent(window);
    glfwSwapInterval(1); // VSync有効化

    if (!gladLoadGLLoader((GLADloadproc)glfwGetProcAddress)) {
        std::cerr << "Failed to initialize GLAD" << std::endl;
        return -1;
    }

    // ------------------------------------------------------------------
    // 2. ImGuiコンテキストの作成 (メイン と セカンダリ)
    // ------------------------------------------------------------------
    IMGUI_CHECKVERSION();

    // --- メインコンテキスト ---
    ImGuiContext* main_context = ImGui::CreateContext();
    ImGui::SetCurrentContext(main_context);
    ImGuiIO& main_io = ImGui::GetIO();
    main_io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;
    ImGui::StyleColorsDark();

    // Initialize backends for main context
    ImGui_ImplGlfw_InitForOpenGL(window, true);
    ImGui_ImplOpenGL3_Init(glsl_version);

    // --- セカンダリ(VM)コンテキスト ---
    ImGuiContext* secondary_context = ImGui::CreateContext();
    ImGui::SetCurrentContext(secondary_context);
    ImGuiIO& secondary_io = ImGui::GetIO();
    secondary_io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;
    // セカンダリコンテキストは独立したスタイルを持つことができる
    ImGui::StyleColorsLight();

    // Initialize OpenGL backend for secondary context (but not GLFW since we'll handle input manually)
    ImGui_ImplOpenGL3_Init(glsl_version);

    // 作業コンテキストをメインに戻す
    ImGui::SetCurrentContext(main_context);

    // ------------------------------------------------------------------
    // 3. VM用の状態変数
    // ------------------------------------------------------------------
    static float vm_counter = 0.0f;
    static float vm_color[3] = { 0.0f, 1.0f, 0.5f };
    static bool vm_show_demo = true;
    static char vm_text_buffer[256] = "Hello from VM!";

    // ------------------------------------------------------------------
    // 4. セカンダリコンテキスト描画用のFBOを作成
    // ------------------------------------------------------------------
    Framebuffer vm_framebuffer;
    if (!CreateFramebuffer(vm_framebuffer, 640, 480)) {
        return -1;
    }


    // ------------------------------------------------------------------
    // 4. メインループ
    // ------------------------------------------------------------------
    while (!glfwWindowShouldClose(window)) {
        glfwPollEvents();

        // === ステップA: セカンダリコンテキストをFBOにレンダリング ===
        {
            // FBOをバインド
            glBindFramebuffer(GL_FRAMEBUFFER, vm_framebuffer.ID);
            glViewport(0, 0, vm_framebuffer.Width, vm_framebuffer.Height);
            glClearColor(0.1f, 0.1f, 0.1f, 1.0f);
            glClear(GL_COLOR_BUFFER_BIT);

            // コンテキストをセカンダリに切り替え
            ImGui::SetCurrentContext(secondary_context);

            // IOを手動で設定（メインコンテキストから情報をコピー）
            secondary_io.DisplaySize = ImVec2((float)vm_framebuffer.Width, (float)vm_framebuffer.Height);
            secondary_io.DeltaTime = main_io.DeltaTime;

            // 入力状態をメインコンテキストからコピー
            secondary_io.MousePos = main_io.MousePos;
            secondary_io.MouseDown[0] = main_io.MouseDown[0];
            secondary_io.MouseDown[1] = main_io.MouseDown[1];
            secondary_io.MouseDown[2] = main_io.MouseDown[2];
            secondary_io.MouseWheel = main_io.MouseWheel;
            secondary_io.MouseWheelH = main_io.MouseWheelH;

            // キーボード入力をコピー
            for (int i = 0; i < IM_ARRAYSIZE(secondary_io.KeysDown); i++) {
                secondary_io.KeysDown[i] = main_io.KeysDown[i];
            }
            secondary_io.KeyCtrl = main_io.KeyCtrl;
            secondary_io.KeyShift = main_io.KeyShift;
            secondary_io.KeyAlt = main_io.KeyAlt;
            secondary_io.KeySuper = main_io.KeySuper;

            // 文字入力をコピー
            if (main_io.InputQueueCharacters.Size > 0) {
                for (int i = 0; i < main_io.InputQueueCharacters.Size; i++) {
                    secondary_io.AddInputCharacter(main_io.InputQueueCharacters[i]);
                }
            }

            // セカンダリのImGuiフレームを開始
            ImGui_ImplOpenGL3_NewFrame();
            ImGui::NewFrame();

            // --- ここにVM内のUIを描画 ---
            ImGui::Begin("VM Control Panel");
            ImGui::Text("Virtual Machine Context - Frame: %.0f", ImGui::GetFrameCount());
            ImGui::Text("Time: %.2f seconds", ImGui::GetTime());
            ImGui::Separator();

            // カウンター
            ImGui::SliderFloat("VM Counter", &vm_counter, 0.0f, 100.0f);
            vm_counter += secondary_io.DeltaTime * 10.0f; // 自動増加
            if (vm_counter > 100.0f) vm_counter = 0.0f;

            // カラーピッカー
            ImGui::ColorEdit3("VM Color", vm_color);

            // テキスト入力
            ImGui::InputText("VM Text", vm_text_buffer, sizeof(vm_text_buffer));

            // ボタン
            if (ImGui::Button("VM Action Button")) {
                std::cout << "VM Button clicked at time: " << ImGui::GetTime() << std::endl;
            }

            // プログレスバー
            ImGui::ProgressBar(vm_counter / 100.0f, ImVec2(0.0f, 0.0f));

            // チェックボックス
            ImGui::Checkbox("Show VM Demo", &vm_show_demo);

            ImGui::End();

            // 条件付きでデモウィンドウを表示
            if (vm_show_demo) {
                ImGui::ShowDemoWindow(&vm_show_demo);
            }

            // レンダリング
            ImGui::Render();
            ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());

            // FBOのバインドを解除
            glBindFramebuffer(GL_FRAMEBUFFER, 0);
        }

        // === ステップB: メインコンテキストを画面にレンダリング ===
        {
            // コンテキストをメインに戻す
            ImGui::SetCurrentContext(main_context);

            // メインのImGuiフレームを開始
            ImGui_ImplOpenGL3_NewFrame();
            ImGui_ImplGlfw_NewFrame();
            ImGui::NewFrame();

            // --- ここにメインのUIを描画 ---

            ImGui::Begin("Main Controller", nullptr, ImGuiWindowFlags_MenuBar);

            // メニューバー
            if (ImGui::BeginMenuBar()) {
                if (ImGui::BeginMenu("VM")) {
                    if (ImGui::MenuItem("Reset VM")) {
                        vm_counter = 0.0f;
                        vm_color[0] = 0.0f; vm_color[1] = 1.0f; vm_color[2] = 0.5f;
                        strcpy(vm_text_buffer, "Hello from VM!");
                    }
                    ImGui::EndMenu();
                }
                ImGui::EndMenuBar();
            }

            ImGui::Text("Main Context - Application %.3f ms/frame (%.1f FPS)",
                       1000.0f / main_io.Framerate, main_io.Framerate);
            ImGui::Separator();
            ImGui::Text("VM Output (Live Rendering):");
            ImGui::Text("VM Counter: %.1f", vm_counter);

            // FBOのテクスチャをImGui::Imageで表示
            // OpenGLのテクスチャはY軸が反転しているため、UV座標を(0,1)-(1,0)に指定して正しく表示
            ImGui::Image(
                (void*)(intptr_t)vm_framebuffer.TextureID,
                ImVec2((float)vm_framebuffer.Width, (float)vm_framebuffer.Height),
                ImVec2(0, 1), // UV0 (Top-left)
                ImVec2(1, 0)  // UV1 (Bottom-right)
            );
            ImGui::End();

            // メインのデモウィンドウ
            ImGui::ShowDemoWindow();

            // 画面へのレンダリング
            int display_w, display_h;
            glfwGetFramebufferSize(window, &display_w, &display_h);
            glViewport(0, 0, display_w, display_h);
            glClearColor(0.45f, 0.55f, 0.60f, 1.00f);
            glClear(GL_COLOR_BUFFER_BIT);

            ImGui::Render();
            ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());
        }

        glfwSwapBuffers(window);
    }

    // ------------------------------------------------------------------
    // 5. クリーンアップ
    // ------------------------------------------------------------------
    DestroyFramebuffer(vm_framebuffer);

    // Cleanup main context
    ImGui::SetCurrentContext(main_context);
    ImGui_ImplOpenGL3_Shutdown();
    ImGui_ImplGlfw_Shutdown();

    // Cleanup secondary context
    ImGui::SetCurrentContext(secondary_context);
    ImGui_ImplOpenGL3_Shutdown();

    // 必ず両方のコンテキストを破棄する
    ImGui::DestroyContext(secondary_context);
    ImGui::DestroyContext(main_context);

    glfwDestroyWindow(window);
    glfwTerminate();

    return 0;
}